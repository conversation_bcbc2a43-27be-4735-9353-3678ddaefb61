# Genesis TO-DO list

## Upcoming tasks

* [x] replace current collision system with Arcade Physics (actually we go for Matter because Arcade does not support rotating collision boundaries)
* [ ] create enemy manager that spawns enemies in waves
* [ ] different enemy types with different movement/shooting patterns
* [ ] basic hub area w/ freeroam movement, level entry & shop
* [ ] configuration system - load config from JSON, save in local storage

## List of things to do in no particular order

* Cutscenes
  * engine for defining cutscene sequences
  * dialogue boxes with text appearing like in RPGs
    * Celeste-eque system for different font/colors/effects
* Menu system
  * controller & keyboard support
  * windows & sub windows
  * drawing menu text
* Levels / maps
  * deeper level editor integration 
  * OR create our own level editor
    * scrollable grid-based viewport, 
  * backgrounds
  * structures (3d models?)
  * lightning effects (player ship casts light on nearby structures)
  * interactive elements / NPC?
* Enemies
  * different kind of enemies
  * custom enemy ai / movement / attack patterns
* Player ship
  * basic movement & shooting
  * attachable, destructible modules
  * ability to equip modules mid-run
  * core ship and modules can be upgraded and modified
  * ship & modules can have shield plates (up to X)
  * combining modules
* UI / HUD
  * decide what elements to show and design the UI
  * ship / module damage UI
* Hub area
  * NPCs, upgrades, level entrances
* Sound & music
* Input system
* Developer tools
  * Respond to HMR events by reloading the current scene
  * Serializable global game configuration object