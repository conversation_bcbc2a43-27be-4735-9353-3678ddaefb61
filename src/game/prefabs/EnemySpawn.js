
// You can write more code here

/* START OF COMPILED CODE */

class EnemySpawn extends Phaser.GameObjects.Triangle {

	constructor(scene, x, y, x1, y1, x2, y2, x3, y3) {
		super(scene, x ?? 644, y ?? 371, x1 ?? 0, y1 ?? 128, x2 ?? 64, y2 ?? 0, x3 ?? 128, y3 ?? 128);

		this.isFilled = true;
		this.fillColor = 16099076;

		/* START-USER-CTR-CODE */
		// Write your code here.
		/* END-USER-CTR-CODE */
	}

	/* START-USER-CODE */

	// Write your code here.

	/* END-USER-CODE */
}

/* END OF COMPILED CODE */

// You can write more code here
