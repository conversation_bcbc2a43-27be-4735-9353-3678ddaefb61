import Phaser from "phaser";
import { Level } from "./Level";
import { GameConfig } from "../config/GameConfig";
import { ResolutionManager } from "../systems/ResolutionManager";

class Debug extends Phaser.Scene {
  // Resolution management
  private gameConfig: GameConfig;
  private resolutionManager: ResolutionManager;

  constructor() {
    super("Debug");

    // Initialize resolution management
    this.gameConfig = GameConfig.getInstance();
    this.resolutionManager = ResolutionManager.getInstance();
  }

  create() {
    const level = this.game.scene.getScene("Level") as Level;

    // Get virtual resolution and scaling
    const virtualRes = this.resolutionManager.getVirtualResolution();
    const targetRes = this.resolutionManager.getTargetResolution();
    const scaleFactor = this.resolutionManager.getScaleFactor();

    // Add resolution info text (positioned in screen coordinates)
    const infoText = this.add.text(
      5 * scaleFactor,
      5 * scaleFactor,
      `Virtual: ${virtualRes.width}x${virtualRes.height}\nTarget: ${targetRes.width}x${targetRes.height}\nScale: ${scaleFactor}x`,
      {
        fontSize: `${3 * scaleFactor}px`,
        color: "#ffffff",
        fontFamily: "Arial",
        backgroundColor: "#000000",
        padding: { x: 2 * scaleFactor, y: 2 * scaleFactor },
      }
    );

    // Create styled button function
    const createStyledButton = (
      x: number,
      y: number,
      text: string,
      callback: () => void
    ) => {
      // Scale button dimensions for target resolution
      const buttonWidth = 40 * scaleFactor; // Scaled for target resolution
      const buttonHeight = 8 * scaleFactor;

      // Background graphics
      const bg = this.add.graphics();
      bg.fillStyle(0x4a5568); // Dark gray background
      bg.fillRoundedRect(
        x - buttonWidth / 2,
        y - buttonHeight / 2,
        buttonWidth,
        buttonHeight,
        6
      );

      // Border
      bg.lineStyle(2, 0x81a3c1); // Light blue border
      bg.strokeRoundedRect(
        x - buttonWidth / 2,
        y - buttonHeight / 2,
        buttonWidth,
        buttonHeight,
        6
      );

      // Inner glow effect
      bg.fillStyle(0x6b7280, 0.3);
      bg.fillRoundedRect(
        x - buttonWidth / 2 + 2,
        y - buttonHeight / 2 + 2,
        buttonWidth - 4,
        buttonHeight - 4,
        4
      );

      // Button text (scaled for virtual resolution)
      const buttonText = this.add
        .text(x, y, text, {
          fontSize: `${4 * scaleFactor}px`, // Scale font size
          color: "#e2e8f0",
          fontFamily: "Arial",
        })
        .setOrigin(0.5);

      // Make interactive
      const hitArea = new Phaser.Geom.Rectangle(
        x - buttonWidth / 2,
        y - buttonHeight / 2,
        buttonWidth,
        buttonHeight
      );
      bg.setInteractive(hitArea, Phaser.Geom.Rectangle.Contains);

      // Hover effects
      bg.on(Phaser.Input.Events.POINTER_OVER, () => {
        bg.clear();
        bg.fillStyle(0x5a6578); // Lighter gray on hover
        bg.fillRoundedRect(
          x - buttonWidth / 2,
          y - buttonHeight / 2,
          buttonWidth,
          buttonHeight,
          6
        );
        bg.lineStyle(2, 0x93b5d1); // Brighter blue border
        bg.strokeRoundedRect(
          x - buttonWidth / 2,
          y - buttonHeight / 2,
          buttonWidth,
          buttonHeight,
          6
        );
        bg.fillStyle(0x7b8794, 0.4);
        bg.fillRoundedRect(
          x - buttonWidth / 2 + 2,
          y - buttonHeight / 2 + 2,
          buttonWidth - 4,
          buttonHeight - 4,
          4
        );
        buttonText.setStyle({ color: "#f1f5f9" });
      });

      bg.on(Phaser.Input.Events.POINTER_OUT, () => {
        bg.clear();
        bg.fillStyle(0x4a5568);
        bg.fillRoundedRect(
          x - buttonWidth / 2,
          y - buttonHeight / 2,
          buttonWidth,
          buttonHeight,
          6
        );
        bg.lineStyle(2, 0x81a3c1);
        bg.strokeRoundedRect(
          x - buttonWidth / 2,
          y - buttonHeight / 2,
          buttonWidth,
          buttonHeight,
          6
        );
        bg.fillStyle(0x6b7280, 0.3);
        bg.fillRoundedRect(
          x - buttonWidth / 2 + 2,
          y - buttonHeight / 2 + 2,
          buttonWidth - 4,
          buttonHeight - 4,
          4
        );
        buttonText.setStyle({ color: "#e2e8f0" });
      });

      bg.on(Phaser.Input.Events.POINTER_DOWN, callback);

      return { bg, text: buttonText };
    };

    // Create buttons (using screen coordinates)
    createStyledButton(
      targetRes.width - 25 * scaleFactor,
      targetRes.height - 28 * scaleFactor,
      "Add Module",
      () => {
        const positions = [
          [8, -3], // Scaled for virtual resolution
          [-8, -3],
          [12, 1],
          [-12, 1],
        ];
        const count = level.getModuleCount();
        if (count >= 4) return;
        const pos = positions[count];
        level.addModule(pos[0], pos[1]);
      }
    );

    createStyledButton(
      targetRes.width - 25 * scaleFactor,
      targetRes.height - 18 * scaleFactor,
      "Remove Module",
      () => {
        level.removeLastModule();
      }
    );

    // Add resolution test button
    createStyledButton(
      targetRes.width - 25 * scaleFactor,
      targetRes.height - 8 * scaleFactor,
      "Test Resolution",
      () => {
        // Cycle through supported resolutions
        const supportedRes = this.gameConfig.getSupportedResolutions();
        const currentRes = this.resolutionManager.getTargetResolution();

        let nextIndex = 0;
        for (let i = 0; i < supportedRes.length; i++) {
          if (
            supportedRes[i].width === currentRes.width &&
            supportedRes[i].height === currentRes.height
          ) {
            nextIndex = (i + 1) % supportedRes.length;
            break;
          }
        }

        const nextRes = supportedRes[nextIndex];
        console.log(
          `Switching to resolution: ${nextRes.width}x${nextRes.height} (${nextRes.scale}x scale)`
        );

        this.gameConfig.setTargetResolution(nextRes.width, nextRes.height);
      }
    );
  }
}

export { Debug };
