import Phaser from "phaser";
import { Level } from "./Level";

class Debug extends Phaser.Scene {
  constructor() {
    super("Debug");
  }

  create() {
    const level = this.game.scene.getScene("Level") as Level;

    // Create styled button function
    const createStyledButton = (
      x: number,
      y: number,
      text: string,
      callback: () => void
    ) => {
      const buttonWidth = 160;
      const buttonHeight = 30;

      // Background graphics
      const bg = this.add.graphics();
      bg.fillStyle(0x4a5568); // Dark gray background
      bg.fillRoundedRect(
        x - buttonWidth / 2,
        y - buttonHeight / 2,
        buttonWidth,
        buttonHeight,
        6
      );

      // Border
      bg.lineStyle(2, 0x81a3c1); // Light blue border
      bg.strokeRoundedRect(
        x - buttonWidth / 2,
        y - buttonHeight / 2,
        buttonWidth,
        buttonHeight,
        6
      );

      // Inner glow effect
      bg.fillStyle(0x6b7280, 0.3);
      bg.fillRoundedRect(
        x - buttonWidth / 2 + 2,
        y - buttonHeight / 2 + 2,
        buttonWidth - 4,
        buttonHeight - 4,
        4
      );

      // Button text
      const buttonText = this.add
        .text(x, y, text, {
          fontSize: "16px",
          color: "#e2e8f0",
          fontFamily: "Arial",
        })
        .setOrigin(0.5);

      // Make interactive
      const hitArea = new Phaser.Geom.Rectangle(
        x - buttonWidth / 2,
        y - buttonHeight / 2,
        buttonWidth,
        buttonHeight
      );
      bg.setInteractive(hitArea, Phaser.Geom.Rectangle.Contains);

      // Hover effects
      bg.on(Phaser.Input.Events.POINTER_OVER, () => {
        bg.clear();
        bg.fillStyle(0x5a6578); // Lighter gray on hover
        bg.fillRoundedRect(
          x - buttonWidth / 2,
          y - buttonHeight / 2,
          buttonWidth,
          buttonHeight,
          6
        );
        bg.lineStyle(2, 0x93b5d1); // Brighter blue border
        bg.strokeRoundedRect(
          x - buttonWidth / 2,
          y - buttonHeight / 2,
          buttonWidth,
          buttonHeight,
          6
        );
        bg.fillStyle(0x7b8794, 0.4);
        bg.fillRoundedRect(
          x - buttonWidth / 2 + 2,
          y - buttonHeight / 2 + 2,
          buttonWidth - 4,
          buttonHeight - 4,
          4
        );
        buttonText.setStyle({ color: "#f1f5f9" });
      });

      bg.on(Phaser.Input.Events.POINTER_OUT, () => {
        bg.clear();
        bg.fillStyle(0x4a5568);
        bg.fillRoundedRect(
          x - buttonWidth / 2,
          y - buttonHeight / 2,
          buttonWidth,
          buttonHeight,
          6
        );
        bg.lineStyle(2, 0x81a3c1);
        bg.strokeRoundedRect(
          x - buttonWidth / 2,
          y - buttonHeight / 2,
          buttonWidth,
          buttonHeight,
          6
        );
        bg.fillStyle(0x6b7280, 0.3);
        bg.fillRoundedRect(
          x - buttonWidth / 2 + 2,
          y - buttonHeight / 2 + 2,
          buttonWidth - 4,
          buttonHeight - 4,
          4
        );
        buttonText.setStyle({ color: "#e2e8f0" });
      });

      bg.on(Phaser.Input.Events.POINTER_DOWN, callback);

      return { bg, text: buttonText };
    };

    // Create buttons
    createStyledButton(
      this.scale.width - 100,
      this.scale.height - 70,
      "Add Module",
      () => {
        const positions = [
          [30, -10],
          [-30, -10],
          [50, 5],
          [-50, 5],
        ];
        const count = level.getModuleCount();
        if (count >= 4) return;
        const pos = positions[count];
        level.addModule(pos[0], pos[1]);
      }
    );

    createStyledButton(
      this.scale.width - 100,
      this.scale.height - 30,
      "Remove Module",
      () => {
        level.removeLastModule();
      }
    );
  }
}

export { Debug };
