import { Scene } from "phaser";
import { InputSystem } from "@/systems/InputSystem";

export class <PERSON>Over extends Scene {
  camera: Phaser.Cameras.Scene2D.Camera;
  background: Phaser.GameObjects.Image;
  gameover_text: Phaser.GameObjects.Text;
  inputSystem: InputSystem;

  constructor() {
    super("GameOver");
  }

  create() {
    this.camera = this.cameras.main;
    this.camera.setBackgroundColor(0x000000);

    this.gameover_text = this.add.text(512, 384, "Game Over", {
      fontFamily: "Arial Black",
      fontSize: 64,
      color: "#ffffff",
      stroke: "#000000",
      strokeThickness: 8,
      align: "center",
    });
    this.gameover_text.setOrigin(0.5);

    this.inputSystem = InputSystem.getInstance();
    this.inputSystem.initialize(this);
  }

  update(): void {
    if (this.inputSystem.released("action1")) {
      console.log("Restarting level");
      this.scene.start("Level");
    }
  }
}
