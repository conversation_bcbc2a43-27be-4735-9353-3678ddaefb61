import { Player } from "@/entities/Player";
import { InputSystem } from "@/systems/InputSystem";
import { BulletManager } from "@/systems/BulletManager";
import { ParticleManager } from "@/systems/ParticleManager";
import { Enemy } from "@/entities/Enemy";
import { Module } from "../entities/Module";
import Phaser from "phaser";
import { Bullet } from "@/entities/Bullet";
import { emit } from "@/systems/EventBus";
import { BulletType } from "@/entities/Bullet";

class Level extends Phaser.Scene {
  // World constants
  public readonly WORLD_WIDTH: number = 1024;
  public readonly WORLD_HEIGHT: number = 768;
  public readonly EDGE_BUFFER: number = 50;
  public readonly REMOVE_DISTANCE: number = 200;

  // Star field properties
  private backStars: Phaser.GameObjects.Graphics[] = [];
  private middleStars: Phaser.GameObjects.Graphics[] = [];
  private frontStars: Phaser.GameObjects.Graphics[] = [];

  // Star speeds for each layer
  private readonly BACK_STAR_SPEED = 0.2;
  private readonly MIDDLE_STAR_SPEED = 0.8;
  private readonly FRONT_STAR_SPEED = 1.5;

  // Player object
  private player: Player;
  private inputSystem: InputSystem;
  private bulletManager: BulletManager;
  private particleManager: ParticleManager;
  private modules: Module[] = [];

  // Enemy system
  private enemies: Enemy[] = [];
  private lastEnemySpawn: number = 0;
  private readonly enemySpawnInterval: number = 250; // 1 second in milliseconds

  // FPS debug logging
  private lastFpsLog: number = 0;
  private readonly fpsLogInterval: number = 1000; // 1 second in milliseconds

  constructor() {
    super("Level");
  }

  create() {
    // Set up cleanup on shutdown event
    this.events.once("shutdown", () => {
      this.cleanup();
    });

    // Create star field before other elements
    this.createStarField();

    // Initialize input system
    this.inputSystem = InputSystem.getInstance();
    this.inputSystem.initialize(this);

    // Create bullet manager (now handles its own groups)
    this.bulletManager = new BulletManager(this);

    // Create particle manager
    this.particleManager = new ParticleManager(this);

    // Create player
    this.player = new Player(this);
    this.player.setBulletManager(this.bulletManager);

    // Set up collision detection using Matter.js collision events
    this.setupCollisions();

    this.scene.launch("Debug");
  }

  private setupCollisions(): void {
    // Set up Matter.js collision detection
    this.matter.world.on("collisionstart", (event: any) => {
      const pairs = event.pairs;

      for (let i = 0; i < pairs.length; i++) {
        const bodyA = pairs[i].bodyA;
        const bodyB = pairs[i].bodyB;
        const gameObjectA = bodyA.gameObject;
        const gameObjectB = bodyB.gameObject;

        if (!gameObjectA || !gameObjectB) continue;

        // Check for bullet-enemy collisions
        if (this.isBulletEnemyCollision(gameObjectA, gameObjectB)) {
          const bullet = this.getBulletFromPair(gameObjectA, gameObjectB);
          const enemy = this.getEnemyFromPair(gameObjectA, gameObjectB);
          if (
            bullet &&
            enemy &&
            bullet.getBulletType() === BulletType.Player &&
            bullet.active
          ) {
            this.handlePlayerBulletEnemyCollision(bullet, enemy);
          }
        }

        // Check for enemy bullet-player collisions
        if (this.isEnemyBulletPlayerCollision(gameObjectA, gameObjectB)) {
          const bullet = this.getBulletFromPair(gameObjectA, gameObjectB);
          if (
            bullet &&
            bullet.getBulletType() === BulletType.Enemy &&
            bullet.active
          ) {
            this.handleEnemyBulletPlayerCollision(this.player, bullet);
          }
        }

        // Check for enemy bullet-module collisions
        if (this.isEnemyBulletModuleCollision(gameObjectA, gameObjectB)) {
          const bullet = this.getBulletFromPair(gameObjectA, gameObjectB);
          const module = this.getModuleFromPair(gameObjectA, gameObjectB);
          if (
            bullet &&
            module &&
            bullet.getBulletType() === BulletType.Enemy &&
            bullet.active
          ) {
            this.handleEnemyBulletModuleCollision(bullet, module);
          }
        }
      }
    });
  }

  // Helper methods for collision detection
  private isBulletEnemyCollision(objA: any, objB: any): boolean {
    return (
      (objA instanceof Bullet && this.enemies.includes(objB)) ||
      (objB instanceof Bullet && this.enemies.includes(objA))
    );
  }

  private isEnemyBulletPlayerCollision(objA: any, objB: any): boolean {
    return (
      (objA instanceof Bullet && objB === this.player) ||
      (objB instanceof Bullet && objA === this.player)
    );
  }

  private isEnemyBulletModuleCollision(objA: any, objB: any): boolean {
    return (
      (objA instanceof Bullet && this.modules.includes(objB)) ||
      (objB instanceof Bullet && this.modules.includes(objA))
    );
  }

  private getBulletFromPair(objA: any, objB: any): Bullet | null {
    if (objA instanceof Bullet) return objA;
    if (objB instanceof Bullet) return objB;
    return null;
  }

  private getEnemyFromPair(objA: any, objB: any): Enemy | null {
    if (this.enemies.includes(objA)) return objA;
    if (this.enemies.includes(objB)) return objB;
    return null;
  }

  private getModuleFromPair(objA: any, objB: any): Module | null {
    if (this.modules.includes(objA)) return objA;
    if (this.modules.includes(objB)) return objB;
    return null;
  }

  private handlePlayerBulletEnemyCollision(
    bulletObj: any,
    enemyObj: any
  ): void {
    const bullet = bulletObj as Bullet;
    const enemy = enemyObj as Enemy;

    // Destroy the bullet
    this.bulletManager.destroyBullet(bullet);

    // Remove enemy from array
    const enemyIndex = this.enemies.indexOf(enemy);
    if (enemyIndex > -1) {
      this.enemies.splice(enemyIndex, 1);
    }

    // Create explosion at enemy position
    this.particleManager.createExplosion(enemy.x, enemy.y);
    emit("enemy:killed", { enemy });
    enemy.destroy();
  }

  private handleEnemyBulletPlayerCollision(_player: any, bullet: any): void {
    // Destroy the bullet
    this.bulletManager.destroyBullet(bullet);

    // Game over
    this.scene.start("GameOver");
  }

  private handleEnemyBulletModuleCollision(bullet: any, module: any): void {
    // Destroy the bullet
    this.bulletManager.destroyBullet(bullet);

    // Remove module from array
    const moduleIndex = this.modules.indexOf(module);
    if (moduleIndex > -1) {
      this.modules.splice(moduleIndex, 1);
    }

    // Create explosion at module position
    this.particleManager.createExplosion(module.x, module.y, 3);
    module.destroy();
  }

  private createStarField(): void {
    const totalStars = Phaser.Math.Between(50, 100);
    const starsPerLayer = Math.floor(totalStars / 3);

    // Create back layer stars (slowest, dimmest)
    for (let i = 0; i < starsPerLayer; i++) {
      const star = this.createStar(0.2, 0.4, 1);
      this.backStars.push(star);
    }

    // Create middle layer stars (medium speed, medium brightness)
    for (let i = 0; i < starsPerLayer; i++) {
      const star = this.createStar(0.4, 0.7, 2);
      this.middleStars.push(star);
    }

    // Create front layer stars (fastest, brightest)
    const remainingStars = totalStars - starsPerLayer * 2;
    for (let i = 0; i < remainingStars; i++) {
      const star = this.createStar(0.7, 1.0, 3);
      this.frontStars.push(star);
    }
  }

  private createStar(
    minAlpha: number,
    maxAlpha: number,
    maxSize: number
  ): Phaser.GameObjects.Graphics {
    const star = this.add.graphics();
    const size = Phaser.Math.Between(1, maxSize);
    const alpha = Phaser.Math.FloatBetween(minAlpha, maxAlpha);

    star.fillStyle(0xffffff, alpha);
    star.fillCircle(0, 0, size);

    // Random position across the screen
    star.x = Phaser.Math.Between(0, this.scale.width);
    star.y = Phaser.Math.Between(0, this.scale.height);

    return star;
  }

  private spawnEnemy(_time: number): void {
    // Spawn enemy near the top of the screen with some randomness
    const spawnY = Phaser.Math.Between(-50, -25); // Just above or at the top of screen
    const spawnX = Phaser.Math.Between(100, this.WORLD_WIDTH - 100); // Random X position with some margin

    // Create enemy with default config (red, medium size, etc.)
    const enemy = new Enemy(
      this,
      spawnX,
      spawnY,
      {
        color: 0xff0000, // Red enemies
        size: 2, // Slightly smaller than default
        speed: 2, // Reduced from 80 for Matter.js
        maxHealth: 1,
        scoreValue: 100,
      },
      this.bulletManager
    );

    // Set particle manager reference for potential future use
    enemy.setParticleManager(this.particleManager);

    // Add to enemy array
    this.enemies.push(enemy);
  }

  private updateEnemies(time: number, delta: number): void {
    const enemiesToRemove: Enemy[] = [];
    const playerPosition = this.player.getPosition();

    // Update each enemy
    this.enemies.forEach((enemy) => {
      enemy.update(time, delta, playerPosition);

      // Remove enemies that are off screen
      if (enemy.isOffScreen()) {
        enemiesToRemove.push(enemy);
      }
    });

    // Clean up off-screen enemies
    enemiesToRemove.forEach((enemy) => {
      const index = this.enemies.indexOf(enemy);
      if (index > -1) {
        this.enemies.splice(index, 1);
        enemy.destroy();
      }
    });
  }

  update(time: number, delta: number): void {
    // Update input system
    this.inputSystem.update();

    // Update bullet manager (handles recycling off-screen bullets)
    this.bulletManager.update(time, delta);

    // Update particle manager
    this.particleManager.update(delta);

    // Update player (shooting is handled internally)
    this.player.update(time, delta);
    this.modules.forEach((module) => module.update(time, delta));

    // Update enemies
    this.updateEnemies(time, delta);

    // Spawn enemies every second
    if (time > this.lastEnemySpawn + this.enemySpawnInterval) {
      this.spawnEnemy(time);
      this.lastEnemySpawn = time;
    }

    // FPS debug logging every second
    if (time > this.lastFpsLog + this.fpsLogInterval) {
      const fps = this.sys.game.loop.actualFps;
      const gameTime = Math.round(time);
      console.log(`Game Time: ${gameTime}ms, FPS: ${fps.toFixed(1)}`);
      this.lastFpsLog = time;
    }

    // Update star positions
    this.updateStarLayer(this.backStars, this.BACK_STAR_SPEED);
    this.updateStarLayer(this.middleStars, this.MIDDLE_STAR_SPEED);
    this.updateStarLayer(this.frontStars, this.FRONT_STAR_SPEED);
  }

  private updateStarLayer(
    stars: Phaser.GameObjects.Graphics[],
    speed: number
  ): void {
    stars.forEach((star) => {
      star.y += speed;

      // Wrap around when star goes off the bottom edge
      if (star.y > this.scale.height + 10) {
        star.y = -10;
        star.x = Phaser.Math.Between(0, this.scale.width);
      }
    });
  }

  private cleanup() {
    // Remove debug scene
    this.scene.stop("Debug");

    // Clean up input system
    if (this.inputSystem) {
      this.inputSystem.destroy();
    }

    // Clean up particle manager
    if (this.particleManager) {
      this.particleManager.destroy();
    }

    // Clean up enemies
    if (this.enemies) {
      this.enemies.forEach((enemy) => enemy.destroy());
      this.enemies = [];
    }

    // Clean up modules
    if (this.modules) {
      this.modules.forEach((module) => module.destroy());
      this.modules = [];
    }
  }

  shutdown() {
    this.cleanup();
  }

  destroy() {
    this.cleanup();
  }

  public getModuleCount(): number {
    return this.modules.length;
  }

  public addModule(offsetX: number, offsetY: number): void {
    if (this.modules.length >= 4) return;
    const module = new Module(this, this.player, offsetX, offsetY);
    module.setBulletManager(this.bulletManager);
    this.modules.push(module);
    emit("module:added", { module });
  }

  public removeLastModule(): void {
    if (this.modules.length > 0) {
      const module = this.modules.pop();
      if (module) {
        emit("module:removed", { module });
        module.destroy();
      }
    }
  }
}

export { Level };
