/*
 * Input system
 * - controller & gamepad support
 * - know whether a key is pressed, held or released
 * - return movement vector
 * - centralized, global manager (singleton)
 */

export interface InputAction {
  keyboard?: string[];
  gamepad?: number[];
}

export interface InputConfig {
  up?: InputAction;
  down?: InputAction;
  left?: InputAction;
  right?: InputAction;
  action1?: InputAction;
  action2?: InputAction;
  action3?: InputAction;
  action4?: InputAction;
  start?: InputAction;
  select?: InputAction;
  [key: string]: InputAction | undefined;
}

export interface GamepadCallbacks {
  onConnect?: (gamepad: Phaser.Input.Gamepad.Gamepad) => void;
  onDisconnect?: (gamepad: Phaser.Input.Gamepad.Gamepad) => void;
}

export enum DualsenseGamepadButton {
  Cross = 0,
  Circle = 1,
  Square = 2,
  Triangle = 3,
  L1 = 4,
  R1 = 5,
  L2 = 6,
  R2 = 7,
  Share = 8,
  Options = 9,
  L3 = 10,
  R3 = 11,
  DpadUp = 12,
  DpadDown = 13,
  DpadLeft = 14,
  DpadRight = 15,
}

export class InputSystem {
  private static instance: InputSystem;
  private keyboard: Phaser.Input.Keyboard.KeyboardPlugin | null = null;
  private gamepadManager: Phaser.Input.Gamepad.GamepadPlugin | null = null;
  private keyboardKeys: Map<string, Phaser.Input.Keyboard.Key> = new Map();
  private activeGamepad: Phaser.Input.Gamepad.Gamepad | null = null;
  private gamepadCallbacks: GamepadCallbacks = {};

  // Button state tracking for gamepad just pressed/released detection
  private previousGamepadButtons: Map<number, boolean> = new Map();
  private currentGamepadButtons: Map<number, boolean> = new Map();

  private inputConfig: InputConfig = {
    up: {
      keyboard: ["UP", "W"],
      gamepad: [DualsenseGamepadButton.DpadUp], // D-pad up
    },
    down: {
      keyboard: ["DOWN", "S"],
      gamepad: [DualsenseGamepadButton.DpadDown], // D-pad down
    },
    left: {
      keyboard: ["LEFT", "A"],
      gamepad: [DualsenseGamepadButton.DpadLeft], // D-pad left
    },
    right: {
      keyboard: ["RIGHT", "D"],
      gamepad: [DualsenseGamepadButton.DpadRight], // D-pad right
    },
    action1: {
      keyboard: ["SPACE", "Z", "J"],
      gamepad: [DualsenseGamepadButton.Cross], // A button (Xbox) / X button (PlayStation)
    },
    action2: {
      keyboard: ["X", "K"],
      gamepad: [DualsenseGamepadButton.Circle], // B button (Xbox) / Circle button (PlayStation)
    },
    action3: {
      keyboard: ["C", "L"],
      gamepad: [DualsenseGamepadButton.Square], // X button (Xbox) / Square button (PlayStation)
    },
    action4: {
      keyboard: ["V", "I"],
      gamepad: [DualsenseGamepadButton.Triangle], // Y button (Xbox) / Triangle button (PlayStation)
    },
    start: {
      keyboard: ["ENTER"],
      gamepad: [DualsenseGamepadButton.Options], // Start button
    },
    select: {
      keyboard: ["SHIFT"],
      gamepad: [DualsenseGamepadButton.Share], // Select/Back button
    },
  };

  public static getInstance(): InputSystem {
    if (!InputSystem.instance) {
      InputSystem.instance = new InputSystem();
    }
    return InputSystem.instance;
  }

  public initialize(scene: Phaser.Scene, callbacks?: GamepadCallbacks): void {
    this.keyboard = scene.input.keyboard;
    this.gamepadManager = scene.input.gamepad;
    this.gamepadCallbacks = callbacks || {};

    this.setupKeyboard();
    this.setupGamepad();
  }

  private setupKeyboard(): void {
    if (!this.keyboard) return;

    // Create keyboard keys for all mapped inputs
    const allKeys = new Set<string>();
    Object.values(this.inputConfig).forEach((action) => {
      if (action?.keyboard) {
        action.keyboard.forEach((key) => allKeys.add(key));
      }
    });

    allKeys.forEach((keyCode) => {
      try {
        const key = this.keyboard!.addKey(keyCode);
        this.keyboardKeys.set(keyCode, key);
      } catch (error) {
        console.warn(`Failed to add keyboard key: ${keyCode}`, error);
      }
    });
  }

  private setupGamepad(): void {
    if (!this.gamepadManager) return;

    // Set up gamepad events
    this.gamepadManager.on(
      "connected",
      (gamepad: Phaser.Input.Gamepad.Gamepad) => {
        console.log("Gamepad connected:", gamepad.id);
        if (!this.activeGamepad) {
          this.activeGamepad = gamepad;
          // Initialize button state tracking for the new gamepad
          this.resetButtonTracking();
        }
        if (this.gamepadCallbacks.onConnect) {
          this.gamepadCallbacks.onConnect(gamepad);
        }
      }
    );

    this.gamepadManager.on(
      "disconnected",
      (gamepad: Phaser.Input.Gamepad.Gamepad) => {
        console.log("Gamepad disconnected:", gamepad.id);
        if (this.activeGamepad === gamepad) {
          // Clear button tracking for disconnected gamepad
          this.resetButtonTracking();

          // Try to find another connected gamepad
          const gamepads = this.gamepadManager!.getAll();
          this.activeGamepad = gamepads.length > 0 ? gamepads[0] : null;

          // Initialize tracking for new gamepad if one was found
          if (this.activeGamepad) {
            this.resetButtonTracking();
          }
        }
        if (this.gamepadCallbacks.onDisconnect) {
          this.gamepadCallbacks.onDisconnect(gamepad);
        }
      }
    );

    // Check for already connected gamepads
    const connectedGamepads = this.gamepadManager.getAll();
    if (connectedGamepads.length > 0 && !this.activeGamepad) {
      this.activeGamepad = connectedGamepads[0];
      console.log("Found existing gamepad:", this.activeGamepad.id);
      // Initialize button state tracking for existing gamepad
      this.resetButtonTracking();
    }
  }

  public updateInputConfig(config: Partial<InputConfig>): void {
    this.inputConfig = { ...this.inputConfig, ...config };

    // Re-setup keyboard with new mappings
    this.keyboardKeys.clear();
    this.setupKeyboard();
  }

  public isGamepadActive(): boolean {
    return this.activeGamepad !== null && this.activeGamepad.connected;
  }

  public getActiveGamepad(): Phaser.Input.Gamepad.Gamepad | null {
    return this.activeGamepad;
  }

  public setGamepadCallbacks(callbacks: GamepadCallbacks): void {
    this.gamepadCallbacks = callbacks;
  }

  public update(): void {
    this.updateGamepadButtonStates();
  }

  private updateGamepadButtonStates(): void {
    if (!this.isGamepadActive()) return;

    // Store previous button states
    this.previousGamepadButtons.clear();
    this.currentGamepadButtons.forEach((pressed, buttonIndex) => {
      this.previousGamepadButtons.set(buttonIndex, pressed);
    });

    // Update current button states
    this.currentGamepadButtons.clear();
    const gamepad = this.activeGamepad!;

    // Track all possible gamepad buttons (0-15 covers most standard gamepads)
    for (let i = 0; i < 16; i++) {
      const button = gamepad.buttons[i];
      if (button) {
        if (button.pressed) {
          console.log("button", i, button.pressed);
        }
        this.currentGamepadButtons.set(i, button.pressed);
      }
    }
  }

  // Button state checking methods
  public isDown(action: string): boolean {
    const inputAction = this.inputConfig[action];
    if (!inputAction) return false;

    // Check keyboard input
    if (inputAction.keyboard) {
      for (const keyCode of inputAction.keyboard) {
        const key = this.keyboardKeys.get(keyCode);
        if (key && key.isDown) {
          return true;
        }
      }
    }

    // Check gamepad input
    if (inputAction.gamepad && this.isGamepadActive()) {
      for (const buttonIndex of inputAction.gamepad) {
        const button = this.activeGamepad!.buttons[buttonIndex];
        if (button && button.pressed) {
          return true;
        }
      }
    }

    return false;
  }

  public isUp(action: string): boolean {
    return !this.isDown(action);
  }

  public pressed(action: string): boolean {
    const inputAction = this.inputConfig[action];
    if (!inputAction) return false;

    // Check keyboard input
    if (inputAction.keyboard) {
      for (const keyCode of inputAction.keyboard) {
        const key = this.keyboardKeys.get(keyCode);
        if (key && Phaser.Input.Keyboard.JustDown(key)) {
          return true;
        }
      }
    }

    // Check gamepad input with proper state tracking
    if (inputAction.gamepad && this.isGamepadActive()) {
      for (const buttonIndex of inputAction.gamepad) {
        const wasPressed =
          this.previousGamepadButtons.get(buttonIndex) || false;
        const isPressed = this.currentGamepadButtons.get(buttonIndex) || false;

        // Just pressed = not pressed before, but pressed now
        if (!wasPressed && isPressed) {
          return true;
        }
      }
    }

    return false;
  }

  public released(action: string): boolean {
    const inputAction = this.inputConfig[action];
    if (!inputAction) return false;

    // Check keyboard input
    if (inputAction.keyboard) {
      for (const keyCode of inputAction.keyboard) {
        const key = this.keyboardKeys.get(keyCode);
        if (key && Phaser.Input.Keyboard.JustUp(key)) {
          return true;
        }
      }
    }

    // Check gamepad input with proper state tracking
    if (inputAction.gamepad && this.isGamepadActive()) {
      for (const buttonIndex of inputAction.gamepad) {
        const wasPressed =
          this.previousGamepadButtons.get(buttonIndex) || false;
        const isPressed = this.currentGamepadButtons.get(buttonIndex) || false;

        // Just released = was pressed before, but not pressed now
        if (wasPressed && !isPressed) {
          return true;
        }
      }
    }

    return false;
  }

  // Movement vector methods
  public getMovementVector(): Phaser.Math.Vector2 {
    const vector = new Phaser.Math.Vector2(0, 0);

    if (this.isDown("left")) vector.x -= 1;
    if (this.isDown("right")) vector.x += 1;
    if (this.isDown("up")) vector.y -= 1;
    if (this.isDown("down")) vector.y += 1;

    // Add analog stick support for gamepad
    if (this.isGamepadActive() && this.activeGamepad!.leftStick) {
      const stick = this.activeGamepad!.leftStick;
      const deadzone = 0.1;

      if (Math.abs(stick.x) > deadzone || Math.abs(stick.y) > deadzone) {
        vector.x = stick.x;
        vector.y = stick.y;
      }
      console.log("stick", stick.x, stick.y);
    }

    return vector.normalize();
  }

  public getRawMovementVector(): Phaser.Math.Vector2 {
    const vector = new Phaser.Math.Vector2(0, 0);

    if (this.isDown("left")) vector.x -= 1;
    if (this.isDown("right")) vector.x += 1;
    if (this.isDown("up")) vector.y -= 1;
    if (this.isDown("down")) vector.y += 1;

    // Add analog stick support for gamepad
    if (this.isGamepadActive() && this.activeGamepad!.leftStick) {
      const stick = this.activeGamepad!.leftStick;
      const deadzone = 0.1;

      if (Math.abs(stick.x) > deadzone || Math.abs(stick.y) > deadzone) {
        vector.x = stick.x;
        vector.y = stick.y;
      }
    }

    return vector;
  }

  // Utility methods
  public getAllMappedActions(): string[] {
    return Object.keys(this.inputConfig);
  }

  public getActionMapping(action: string): InputAction | undefined {
    return this.inputConfig[action];
  }

  private resetButtonTracking(): void {
    this.previousGamepadButtons.clear();
    this.currentGamepadButtons.clear();
  }

  public destroy(): void {
    if (this.gamepadManager) {
      this.gamepadManager.off("connected");
      this.gamepadManager.off("disconnected");
    }

    this.keyboardKeys.clear();
    this.activeGamepad = null;
    this.keyboard = null;
    this.gamepadManager = null;
  }
}
