import Phaser from "phaser";

interface Particle extends Phaser.GameObjects.Graphics {
  velocityX: number;
  velocityY: number;
  life: number;
  initialAlpha: number;
  decay: number;
}

class ParticleManager {
  private scene: Phaser.Scene;
  private particles: Phaser.GameObjects.Group;

  constructor(scene: Phaser.Scene) {
    this.scene = scene;
    this.particles = scene.add.group();
  }

  public createExplosion(x: number, y: number, size = 4): void {
    const particleCount = Math.floor(12 * (size / 4));
    const colors = [0xff0000, 0xff4400, 0xff8800, 0xffaa00, 0xffff00]; // Red to yellow gradient

    for (let i = 0; i < particleCount; i++) {
      // Create particle
      const particle = this.scene.add.graphics() as Particle;
      const color = colors[Phaser.Math.Between(0, colors.length - 1)];
      particle.fillStyle(color);
      const particleSize = Phaser.Math.Between(2 * (size / 4), 4 * (size / 4));
      particle.fillCircle(0, 0, particleSize);

      // Position at explosion center
      particle.x = x;
      particle.y = y;

      // Random velocity in all directions
      const angle =
        (i / particleCount) * Math.PI * 2 + Phaser.Math.FloatBetween(-0.3, 0.3);
      const speed = Phaser.Math.Between(80 * (size / 4), 150 * (size / 4));
      particle.velocityX = Math.cos(angle) * speed;
      particle.velocityY = Math.sin(angle) * speed;

      // Particle properties
      particle.life = 1.0 * (size / 4); // Lifetime scales with size
      particle.initialAlpha = 1.0;
      particle.decay = 0.95; // Velocity decay factor

      this.particles.add(particle);
    }
  }

  public createPlayerExplosion(x: number, y: number): number {
    const particleCount = 24; // More particles for bigger explosion
    const colors = [0xff0000, 0xff4400, 0xff8800, 0xffaa00, 0xffff00, 0xffffff]; // Red to white gradient

    for (let i = 0; i < particleCount; i++) {
      // Create particle
      const particle = this.scene.add.graphics() as Particle;
      const color = colors[Phaser.Math.Between(0, colors.length - 1)];
      particle.fillStyle(color);
      particle.fillCircle(0, 0, Phaser.Math.Between(3, 7)); // Larger particles

      // Position at explosion center
      particle.x = x;
      particle.y = y;

      // Random velocity in all directions with higher speeds
      const angle =
        (i / particleCount) * Math.PI * 2 + Phaser.Math.FloatBetween(-0.5, 0.8);
      const speed = Phaser.Math.Between(120, 250); // Faster particles
      particle.velocityX = Math.cos(angle) * speed;
      particle.velocityY = Math.sin(angle) * speed;

      // Particle properties
      particle.life = 1.2;
      particle.initialAlpha = 0.6;
      particle.decay = 0.95;

      this.particles.add(particle);
    }

    return 1500; // 1.5 seconds
  }

  public update(delta: number): void {
    const deltaSeconds = delta / 1000;
    const particlesToRemove: Particle[] = [];

    const particleArray = Array.from(this.particles.children) as Particle[];
    particleArray.forEach((particle) => {
      // Update particle position
      particle.x += particle.velocityX * deltaSeconds;
      particle.y += particle.velocityY * deltaSeconds;

      // Apply velocity decay (particles slow down over time)
      particle.velocityX *= particle.decay;
      particle.velocityY *= particle.decay;

      // Update particle life and alpha
      particle.life -= deltaSeconds;
      const lifeRatio = Math.max(0, particle.life);
      particle.alpha = particle.initialAlpha * lifeRatio;

      if (particle.life <= 0) {
        particlesToRemove.push(particle);
      }
    });

    particlesToRemove.forEach((particle) => {
      this.particles.remove(particle);
      particle.destroy();
    });
  }

  public destroy(): void {
    if (this.particles && this.particles.children) {
      const particleArray = Array.from(this.particles.children);
      particleArray.forEach((particle) => {
        particle.destroy();
      });
      this.particles.destroy();
    }
  }
}

export { ParticleManager };
