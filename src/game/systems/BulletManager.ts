// You can write more code here

/* START OF COMPILED CODE */

import { Bullet, BulletType } from "@/entities/Bullet";

class BulletManager {
  private scene: Phaser.Scene;
  private playerBullets: Phaser.GameObjects.Group;
  private enemyBullets: Phaser.GameObjects.Group;

  // World bounds
  private WORLD_WIDTH: number;
  private WORLD_HEIGHT: number;
  private BULLET_BUFFER: number;

  constructor(scene: Phaser.Scene) {
    this.scene = scene;

    // Get world constants from scene
    this.WORLD_WIDTH = (scene as any).WORLD_WIDTH || 1024;
    this.WORLD_HEIGHT = (scene as any).WORLD_HEIGHT || 768;
    this.BULLET_BUFFER = (scene as any).BULLET_BUFFER || 50;

    // Create groups for bullet management (Matter.js doesn't use physics groups the same way)
    this.playerBullets = scene.add.group({
      classType: Bullet,
      maxSize: 200,
      runChildUpdate: false,
    });

    this.enemyBullets = scene.add.group({
      classType: Bullet,
      maxSize: 200,
      runChildUpdate: false,
    });
  }

  public fireBullet(
    x: number,
    y: number,
    rotation: number,
    bulletType: BulletType
  ): void {
    const group =
      bulletType === BulletType.Player ? this.playerBullets : this.enemyBullets;

    // Get a bullet from the pool
    const bullet = group.get(x, y) as Bullet;

    if (bullet) {
      // Add to Matter physics world if not already added
      if (!(bullet as any).body) {
        this.scene.matter.add.gameObject(bullet);
      }

      // Fire the bullet (this will activate, position, and set velocity)
      bullet.fire(x, y, rotation, bulletType);
    }
  }

  public update(time: number, delta: number): void {
    // Check for off-screen bullets and recycle them
    [this.playerBullets, this.enemyBullets].forEach((group) => {
      group.children.forEach((child) => {
        const bullet = child as Bullet;
        if (bullet.active && bullet.visible && bullet.isOffScreen()) {
          group.killAndHide(bullet);
          (bullet as any).body.enable = false; // Disable physics body to stop updates
        }
        return true;
      });
    });
  }

  public getPlayerBulletGroup(): Phaser.GameObjects.Group {
    return this.playerBullets;
  }

  public getEnemyBulletGroup(): Phaser.GameObjects.Group {
    return this.enemyBullets;
  }

  public getPlayerBullets(): Bullet[] {
    return this.playerBullets
      .getChildren()
      .filter((child) => child.active) as Bullet[];
  }

  public getEnemyBullets(): Bullet[] {
    return this.enemyBullets
      .getChildren()
      .filter((child) => child.active) as Bullet[];
  }

  public destroyBullet(bullet: Bullet): void {
    // Immediately mark as inactive to prevent multiple collision processing
    bullet.setActive(false);
    bullet.setVisible(false);

    const group =
      bullet.getBulletType() === BulletType.Player
        ? this.playerBullets
        : this.enemyBullets;
    group.killAndHide(bullet);
    (bullet as any).body.enable = false;
  }

  public clear(): void {
    this.playerBullets.clear(true, true);
    this.enemyBullets.clear(true, true);
  }
}

export { BulletManager };
