import { Boot } from "./scenes/Boot";
import { GameOver } from "./scenes/GameOver";
import { Level } from "./scenes/Level";
import { AUTO, Game } from "phaser";
import { Preloader } from "./scenes/Preloader";
import { Debug } from "./scenes/Debug";

// Find out more information about the Game Config at:
// https://docs.phaser.io/api-documentation/typedef/types-core#gameconfig
const config: Phaser.Types.Core.GameConfig = {
  type: AUTO,
  width: 1024,
  height: 768,
  parent: "game-container",
  backgroundColor: "#000000",
  scene: [Boot, Preloader, Level, GameOver, Debug],
  physics: {
    default: "matter",
    matter: {
      enableSleeping: false,
      gravity: {
        x: 0,
        y: 0,
      },
      debug: {
        showBody: false,
        showStaticBody: false,
        showVelocity: false,
        showCollisions: false,
      },
    },
  },
  input: {
    gamepad: true,
  },
};

const StartGame = (parent: string) => {
  return new Game({ ...config, parent });
};

export default StartGame;
