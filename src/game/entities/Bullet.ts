export enum BulletType {
  Player = "player",
  Enemy = "enemy",
}

class Bullet extends Phaser.Physics.Matter.Sprite {
  // Bullet type
  public bulletType: BulletType = BulletType.Player; // Default to player

  // World bounds (will be set from scene)
  private WORLD_WIDTH: number;
  private WORLD_HEIGHT: number;
  private BULLET_BUFFER: number;

  // Constants
  private readonly BULLET_SCALE: number = 0.25; // Scale to maintain current size

  constructor(
    scene: Phaser.Scene,
    x: number = 0,
    y: number = 0,
    texture: string,
    frame?: number | string
  ) {
    super(scene.matter.world, x, y, texture || "bullet", frame);

    // Get world constants from scene (assuming they exist)
    this.WORLD_WIDTH = (scene as any).WORLD_WIDTH || 1024;
    this.WORLD_HEIGHT = (scene as any).WORLD_HEIGHT || 768;
    this.BULLET_BUFFER = (scene as any).BULLET_BUFFER || 50;

    // Create bullet visuals
    this.createBulletVisuals();
  }

  private setupPhysicsBody(): void {
    // Set collision bounds - small rectangle for bullet
    const bodySize = 4;
    this.setRectangle(bodySize, bodySize);

    // Configure physics properties - no friction for fast bullets
    this.setBounce(0); // No bounce
    this.setIgnoreGravity(true); // Ignore world gravity
    this.setFrictionAir(0); // No air resistance for bullets

    // Set collision categories based on bullet type
    const playerCategory = 0x0001;
    const enemyCategory = 0x0002;
    const playerBulletCategory = 0x0003;
    const enemyBulletCategory = 0x0004;
    const moduleCategory = 0x0005;

    if (this.bulletType === BulletType.Player) {
      this.setCollisionCategory(playerBulletCategory);
      this.setCollidesWith([enemyCategory]);
    } else {
      this.setCollisionCategory(enemyBulletCategory);
      this.setCollidesWith([playerCategory, moduleCategory]);
    }
  }

  private createBulletVisuals(): void {
    // Set up the sprite
    this.setScale(this.BULLET_SCALE);

    // Set tint to yellow for bullets
    this.setTint(0xffff00);
  }

  // Initialize method called when a bullet is fired (pooling system)
  public fire(
    startX: number,
    startY: number,
    rotation: number,
    type: BulletType
  ): void {
    // Reset state for pooling
    this.setActive(true);
    this.setVisible(true);

    // Set position
    this.x = startX;
    this.y = startY;

    // Set type and calculate speed
    this.bulletType = type;
    const bulletSpeed = this.bulletType === BulletType.Player ? 15 : 8; // Reduced from 600/300 for Matter.js

    // Configure physics body
    this.setupPhysicsBody();

    // Set velocity based on rotation
    const velocityX = Math.sin(rotation) * bulletSpeed;
    const velocityY = -Math.cos(rotation) * bulletSpeed;
    this.setVelocity(velocityX, velocityY);
  }

  public update(_time: number, delta: number): void {
    // Physics handles movement automatically, no manual updates needed
  }

  public isOffScreen(): boolean {
    return (
      this.x < -this.BULLET_BUFFER ||
      this.x > this.WORLD_WIDTH + this.BULLET_BUFFER ||
      this.y < -this.BULLET_BUFFER ||
      this.y > this.WORLD_HEIGHT + this.BULLET_BUFFER
    );
  }

  public getBulletType(): BulletType {
    return this.bulletType;
  }

  public destroy(): void {
    // Call parent destroy
    super.destroy();
  }
}

export { Bullet };
