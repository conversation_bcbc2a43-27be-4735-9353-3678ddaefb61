---
type: "always_apply"
---

Genesis is a 2D space shooter game built with Phaser.js and Vite. It's a top-down arcade-style game where the player controls a spaceship, navigating through a vertically-scrolling world while fighting enemies and avoiding obstacles.

Rules:
- Use bun as a runtime and package manager.
- When you're done with a task, output only a short, once sentence summary of the changes you made.
- Do not start the dev server after you're done with a task. The server is already running.
- Do not test your implementation, after you're done just output a *short* summary of changes.
- Classes should export themselves at the end of the file like:
  export { Title };